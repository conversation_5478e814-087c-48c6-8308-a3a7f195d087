from typing import Dict
import sys
import os

from aiogram.fsm.context import FSMContext
from aiogram.types import CallbackQuery
from common.utils import check_if_id_in_callback_data

from common.analytics.keyboards import get_back_to_analytics_kb

# Добавляем путь к корневой папке проекта для импорта database
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database import (
    StudentRepository,
    SubjectRepository,
    MicrotopicRepository,
    HomeworkResultRepository,
    CourseRepository,
    GroupRepository
)


async def format_microtopic_stats(student_id: int, subject_id: int, format_type: str = "detailed") -> dict:
    """
    Получить и отформатировать статистику по микротемам для студента

    Args:
        student_id: ID студента
        subject_id: ID предмета
        format_type: Тип форматирования ("detailed" - с процентами и эмодзи, "summary" - только сильные/слабые)

    Returns:
        dict: Словарь с отформатированными данными
    """
    # Получаем понимание по микротемам
    microtopic_stats = await StudentRepository.get_microtopic_understanding(
        student_id, subject_id
    )

    if not microtopic_stats:
        return {
            'text': "❌ Пока не выполнено ни одного задания по микротемам этого предмета",
            'has_data': False
        }

    # Получаем названия микротем
    microtopics = await MicrotopicRepository.get_by_subject(subject_id)
    microtopic_names = {mt.number: mt.name for mt in microtopics}

    strong_topics = []  # ≥80%
    weak_topics = []    # ≤40%

    if format_type == "detailed":
        # Формируем список микротем с процентами и эмодзи
        result_text = "📈 % понимания по микротемам:\n"

        for number, stats in microtopic_stats.items():
            name = microtopic_names.get(number, f"Микротема {number}")
            percentage = stats['percentage']

            # Определяем эмодзи статуса
            if percentage >= 80:
                status = "✅"
                strong_topics.append(name)
            elif percentage <= 40:
                status = "❌"
                weak_topics.append(name)
            else:
                status = "⚠️"

            result_text += f"• {name} — {percentage:.0f}% {status}\n"

        return {
            'text': result_text.strip(),
            'has_data': True,
            'strong_topics': strong_topics,
            'weak_topics': weak_topics
        }

    elif format_type == "summary":
        # Определяем сильные и слабые темы
        for number, stats in microtopic_stats.items():
            name = microtopic_names.get(number, f"Микротема {number}")
            percentage = stats['percentage']

            if percentage >= 80:
                strong_topics.append(name)
            elif percentage <= 40:
                weak_topics.append(name)

        # Формируем сводку по сильным и слабым темам
        result_text = ""
        if strong_topics:
            result_text += "🟢 Сильные темы (≥80%):\n"
            for topic in strong_topics:
                result_text += f"• {topic}\n"

        if weak_topics:
            if result_text:
                result_text += "\n"
            result_text += "🔴 Слабые темы (≤40%):\n"
            for topic in weak_topics:
                result_text += f"• {topic}\n"

        if not strong_topics and not weak_topics:
            result_text = "📊 Нет сильных или слабых тем"

        return {
            'text': result_text.strip(),
            'has_data': True,
            'strong_topics': strong_topics,
            'weak_topics': weak_topics
        }


async def get_real_student_analytics(student_id: int) -> str:
    """
    Получить реальную статистику студента из базы данных

    Args:
        student_id: ID студента

    Returns:
        str: Отформатированный текст со статистикой
    """
    try:
        # Получаем студента
        student = await StudentRepository.get_by_id(student_id)
        if not student:
            return "❌ Студент не найден"

        # Получаем общую статистику
        general_stats = await StudentRepository.get_general_stats(student_id)

        # Формируем текст результата
        result_text = f"👤 Студент: {student.user.name}\n"
        result_text += f"📚 Группа: {student.group.name if student.group else 'Не назначена'}\n"
        result_text += f"💎 Тариф: {student.tariff or 'Не указан'}\n\n"

        result_text += f"📊 Общая статистика:\n"
        result_text += f"   • Баллы: {general_stats.get('total_points', 0)}\n"
        result_text += f"   • Уровень: {student.level}\n"
        result_text += f"   • Выполнено ДЗ: {general_stats.get('unique_completed', 0)} (всего попыток: {general_stats.get('total_completed', 0)})\n"

        # Если студент в группах, показываем статистику по предметам
        if student.groups:
            # Получаем уникальные предметы из всех групп студента
            subjects = []
            for group in student.groups:
                if group.subject and group.subject not in subjects:
                    subjects.append(group.subject)

            for subject in subjects:
                result_text += f"\n📗 Прогресс по предмету '{subject.name}':\n"

                # Получаем отформатированную статистику по микротемам (детальный формат)
                # НЕ показываем непроверенные микротемы (show_untested=False по умолчанию)
                microtopic_data = await format_microtopic_stats(student_id, subject.id, "detailed")

                if microtopic_data['has_data']:
                    result_text += microtopic_data['text']

                    # Добавляем сводку по сильным и слабым темам
                    summary_data = await format_microtopic_stats(student_id, subject.id, "summary")
                    result_text += f"\n\n{summary_data['text']}"
                else:
                    result_text += microtopic_data['text']

        return result_text

    except Exception as e:
        return f"❌ Ошибка при получении статистики: {str(e)}"


async def get_student_topics_stats(student_id: str) -> Dict:
    """
    Получить статистику по темам для конкретного ученика

    Args:
        student_id: ID ученика

    Returns:
        Dict: Словарь с данными о студенте и его прогрессе по темам
    """
    try:
        from database.repositories import StudentRepository

        # Получаем реального студента из базы данных
        student_id_int = int(student_id)
        student = await StudentRepository.get_by_id(student_id_int)

        if not student:
            return {"name": "Студент не найден", "topics": {}}

        # Пока возвращаем базовую информацию
        # В будущем здесь будет реальная статистика по микротемам
        return {
            "name": student.user.name,
            "topics": {}  # Пока пустой словарь, в будущем - реальная статистика
        }

    except (ValueError, Exception) as e:
        print(f"Ошибка при получении статистики студента {student_id}: {e}")
        return {"name": "Ошибка загрузки", "topics": {}}

async def get_group_stats(group_id: str) -> Dict:
    """
    Получить статистику по группе

    Args:
        group_id: ID группы

    Returns:
        Dict: Словарь с данными о группе и статистике
    """
    try:
        from database.repositories import GroupRepository, StudentRepository

        # Получаем группу
        group = await GroupRepository.get_by_id(int(group_id))
        if not group:
            return {
                "name": "Неизвестная группа",
                "subject": "Неизвестный предмет",
                "homework_completion": 0,
                "topics": {},
                "rating": []
            }

        # Получаем студентов группы
        students = await StudentRepository.get_by_group(int(group_id))

        # Вычисляем статистику
        student_ratings = []
        topics_stats = {}
        total_homework_percentage = 0

        for student in students:
            # Получаем общую статистику студента
            student_stats = await StudentRepository.get_general_stats(student.id)

            # Вычисляем средний процент выполнения ДЗ для студента
            # Используем unique_completed (уникальные выполненные) / total_available (всего доступных)
            if student_stats.get('total_available', 0) > 0:
                student_homework_percentage = (student_stats.get('unique_completed', 0) / student_stats.get('total_available', 1)) * 100
            else:
                student_homework_percentage = 0
            total_homework_percentage += student_homework_percentage

            # Добавляем в рейтинг
            student_ratings.append({
                "name": student.user.name,
                "points": student_stats.get('total_points', 0)
            })

            # Получаем статистику по микротемам для предмета группы
            if group.subject:
                microtopic_stats = await StudentRepository.get_microtopic_understanding(student.id, group.subject.id)
                for microtopic_number, stats in microtopic_stats.items():
                    if microtopic_number not in topics_stats:
                        topics_stats[microtopic_number] = []
                    topics_stats[microtopic_number].append(stats['percentage'])

        # Вычисляем средние значения
        avg_homework_completion = round(total_homework_percentage / len(students), 1) if students else 0

        # Получаем названия микротем
        from database.repositories import MicrotopicRepository
        avg_topics = {}
        if group.subject:
            microtopics = await MicrotopicRepository.get_by_subject(group.subject.id)
            microtopic_names = {mt.number: mt.name for mt in microtopics}

            for microtopic_number, percentages in topics_stats.items():
                microtopic_name = microtopic_names.get(microtopic_number, f"Микротема {microtopic_number}")
                avg_topics[microtopic_name] = round(sum(percentages) / len(percentages), 1) if percentages else 0

        # Сортируем рейтинг по баллам
        student_ratings.sort(key=lambda x: x["points"], reverse=True)

        # Получаем средний рост по тест-отчетам для группы
        test_reports_growth = await get_group_test_reports_growth_weighted(int(group_id))

        return {
            "name": group.name,
            "subject": group.subject.name if group.subject else "Неизвестный предмет",
            "homework_completion": avg_homework_completion,
            "test_reports_growth": round(test_reports_growth, 1) if test_reports_growth is not None else None,
            "topics": avg_topics,
            "rating": student_ratings[:10]  # Топ 10 студентов
        }

    except Exception as e:
        print(f"Ошибка при получении статистики группы: {e}")
        return {
            "name": "Ошибка загрузки",
            "subject": "Ошибка загрузки",
            "homework_completion": 0,
            "topics": {},
            "rating": []
        }

async def format_student_topics_stats_real(student_id: int, subject_id: int, format_type: str = "detailed") -> str:
    """
    Форматировать статистику по темам ученика из реальных данных БД

    Args:
        student_id: ID студента
        subject_id: ID предмета
        format_type: Тип форматирования ("detailed" или "summary")

    Returns:
        str: Отформатированный текст со статистикой
    """
    # Получаем студента для имени
    student = await StudentRepository.get_by_id(student_id)
    if not student:
        return "❌ Студент не найден"

    # Получаем отформатированную статистику по микротемам
    microtopic_data = await format_microtopic_stats(student_id, subject_id, format_type)

    if not microtopic_data['has_data']:
        return f"📌 {student.user.name}\n{microtopic_data['text']}"

    result_text = f"📌 {student.user.name}\n{microtopic_data['text']}"

    # Если это детальный формат, добавляем сводку
    if format_type == "detailed":
        summary_data = await format_microtopic_stats(student_id, subject_id, "summary")
        result_text += f"\n\n{summary_data['text']}"

    return result_text


async def get_student_microtopics_detailed(student_id: int, subject_id: int) -> str:
    """
    Получить детальную статистику по микротемам для отдельной кнопки

    Args:
        student_id: ID студента
        subject_id: ID предмета

    Returns:
        str: Отформатированный текст с детальной статистикой
    """
    # Получаем студента для имени
    student = await StudentRepository.get_by_id(student_id)
    if not student:
        return "❌ Студент не найден"

    # Получаем отформатированную статистику по микротемам
    microtopic_data = await format_microtopic_stats(student_id, subject_id, "detailed")

    if not microtopic_data['has_data']:
        return f"📌 {student.user.name}\n{microtopic_data['text']}"

    # Показываем только проверенные микротемы (НЕ показываем непроверенные)
    return f"📌 {student.user.name}\n{microtopic_data['text']}"


async def get_student_strong_weak_summary(student_id: int, subject_id: int) -> str:
    """
    Получить сводку по сильным и слабым темам для отдельной кнопки

    Args:
        student_id: ID студента
        subject_id: ID предмета

    Returns:
        str: Отформатированный текст со сводкой
    """
    # Получаем студента для имени
    student = await StudentRepository.get_by_id(student_id)
    if not student:
        return "❌ Студент не найден"

    # Получаем сводку по сильным и слабым темам
    summary_data = await format_microtopic_stats(student_id, subject_id, "summary")

    if not summary_data['has_data']:
        return f"📌 {student.user.name}\n❌ Пока не выполнено ни одного задания для анализа сильных и слабых тем"

    return f"📌 {student.user.name}\n{summary_data['text']}"


async def get_general_microtopics_detailed() -> str:
    """
    Получить детальную статистику по микротемам для всех предметов

    Returns:
        str: Отформатированный текст с детальной статистикой
    """
    try:
        from database.repositories import SubjectRepository, GroupRepository, StudentRepository, MicrotopicRepository

        # Получаем все предметы
        all_subjects = await SubjectRepository.get_all()
        if not all_subjects:
            return "❌ Предметы не найдены"

        result_text = "📊 Общая статистика по микротемам\n📈 Средний % понимания по всем предметам:\n\n"

        has_data = False

        for subject in all_subjects:
            # Получаем группы предмета
            groups = await GroupRepository.get_by_subject(subject.id)
            if not groups:
                continue

            # Получаем микротемы предмета
            microtopics = await MicrotopicRepository.get_by_subject(subject.id)
            if not microtopics:
                continue

            # Собираем статистику по всем микротемам предмета
            microtopic_stats = {}

            for group in groups:
                students = await StudentRepository.get_by_group(group.id)

                for student in students:
                    student_microtopic_stats = await StudentRepository.get_microtopic_understanding(student.id, subject.id)

                    for microtopic_number, stats in student_microtopic_stats.items():
                        if microtopic_number not in microtopic_stats:
                            microtopic_stats[microtopic_number] = []
                        microtopic_stats[microtopic_number].append(stats['percentage'])

            if microtopic_stats:
                has_data = True
                result_text += f"📚 {subject.name}:\n"

                # Создаем словарь названий микротем
                microtopic_names = {mt.number: mt.name for mt in microtopics}

                # Сортируем по номеру микротемы
                for microtopic_number in sorted(microtopic_stats.keys()):
                    percentages = microtopic_stats[microtopic_number]
                    avg_percentage = round(sum(percentages) / len(percentages), 1) if percentages else 0
                    microtopic_name = microtopic_names.get(microtopic_number, f"Микротема {microtopic_number}")

                    # Определяем статус
                    status = "✅" if avg_percentage >= 80 else "❌" if avg_percentage <= 40 else "⚠️"
                    result_text += f"  • {microtopic_name} — {avg_percentage}% {status}\n"

                result_text += "\n"

        if not has_data:
            return "📊 Общая статистика по микротемам\n❌ Пока не выполнено ни одного задания по микротемам"

        return result_text.rstrip()

    except Exception as e:
        print(f"Ошибка при получении общей детальной статистики: {e}")
        return "❌ Ошибка при получении статистики"


async def get_general_microtopics_summary() -> str:
    """
    Получить сводку по сильным и слабым темам для всех предметов

    Returns:
        str: Отформатированный текст со сводкой
    """
    try:
        from database.repositories import SubjectRepository, GroupRepository, StudentRepository, MicrotopicRepository

        # Получаем все предметы
        all_subjects = await SubjectRepository.get_all()
        if not all_subjects:
            return "❌ Предметы не найдены"

        result_text = "📊 Общая сводка по микротемам\n"

        all_strong_topics = []
        all_weak_topics = []
        has_data = False

        for subject in all_subjects:
            # Получаем группы предмета
            groups = await GroupRepository.get_by_subject(subject.id)
            if not groups:
                continue

            # Получаем микротемы предмета
            microtopics = await MicrotopicRepository.get_by_subject(subject.id)
            if not microtopics:
                continue

            # Собираем статистику по всем микротемам предмета
            microtopic_stats = {}

            for group in groups:
                students = await StudentRepository.get_by_group(group.id)

                for student in students:
                    student_microtopic_stats = await StudentRepository.get_microtopic_understanding(student.id, subject.id)

                    for microtopic_number, stats in student_microtopic_stats.items():
                        if microtopic_number not in microtopic_stats:
                            microtopic_stats[microtopic_number] = []
                        microtopic_stats[microtopic_number].append(stats['percentage'])

            if microtopic_stats:
                has_data = True
                # Создаем словарь названий микротем
                microtopic_names = {mt.number: mt.name for mt in microtopics}

                # Вычисляем средние значения и определяем сильные/слабые темы
                for microtopic_number, percentages in microtopic_stats.items():
                    avg_percentage = round(sum(percentages) / len(percentages), 1) if percentages else 0
                    microtopic_name = microtopic_names.get(microtopic_number, f"Микротема {microtopic_number}")
                    topic_with_subject = f"{microtopic_name} ({subject.name})"

                    if avg_percentage >= 80:
                        all_strong_topics.append(topic_with_subject)
                    elif avg_percentage <= 40:
                        all_weak_topics.append(topic_with_subject)

        if not has_data:
            return "📊 Общая сводка по микротемам\n❌ Пока не выполнено ни одного задания для анализа"

        # Формируем результат
        if all_strong_topics:
            result_text += "\n🟢 Сильные темы (≥80%):\n"
            for topic in all_strong_topics:
                result_text += f"• {topic}\n"

        if all_weak_topics:
            if all_strong_topics:
                result_text += "\n"
            result_text += "🔴 Слабые темы (≤40%):\n"
            for topic in all_weak_topics:
                result_text += f"• {topic}\n"

        if not all_strong_topics and not all_weak_topics:
            result_text += "\n📊 Нет сильных или слабых тем"

        return result_text

    except Exception as e:
        print(f"Ошибка при получении общей сводки: {e}")
        return "❌ Ошибка при получении сводки"


async def get_subject_microtopics_detailed(subject_id: int) -> str:
    """
    Получить детальную статистику по микротемам для предмета

    Args:
        subject_id: ID предмета

    Returns:
        str: Отформатированный текст с детальной статистикой
    """
    try:
        from database.repositories import SubjectRepository, GroupRepository, StudentRepository, MicrotopicRepository

        # Получаем предмет
        subject = await SubjectRepository.get_by_id(subject_id)
        if not subject:
            return "❌ Предмет не найден"

        # Получаем группы предмета
        groups = await GroupRepository.get_by_subject(subject_id)
        if not groups:
            return f"📚 {subject.name}\n❌ Группы по данному предмету не найдены"

        # Получаем микротемы предмета
        microtopics = await MicrotopicRepository.get_by_subject(subject_id)
        if not microtopics:
            return f"📚 {subject.name}\n❌ Микротемы по данному предмету не найдены"

        # Собираем статистику по всем микротемам
        microtopic_stats = {}
        total_students = 0

        for group in groups:
            students = await StudentRepository.get_by_group(group.id)
            total_students += len(students)

            for student in students:
                student_microtopic_stats = await StudentRepository.get_microtopic_understanding(student.id, subject_id)

                for microtopic_number, stats in student_microtopic_stats.items():
                    if microtopic_number not in microtopic_stats:
                        microtopic_stats[microtopic_number] = []
                    microtopic_stats[microtopic_number].append(stats['percentage'])

        if not microtopic_stats:
            return f"📚 {subject.name}\n❌ Пока не выполнено ни одного задания по микротемам этого предмета"

        # Создаем словарь названий микротем
        microtopic_names = {mt.number: mt.name for mt in microtopics}

        # Формируем результат
        result_text = f"📚 {subject.name}\n📈 Средний % понимания по микротемам:\n"

        # Сортируем по номеру микротемы
        for microtopic_number in sorted(microtopic_stats.keys()):
            percentages = microtopic_stats[microtopic_number]
            avg_percentage = round(sum(percentages) / len(percentages), 1) if percentages else 0
            microtopic_name = microtopic_names.get(microtopic_number, f"Микротема {microtopic_number}")

            # Определяем статус
            status = "✅" if avg_percentage >= 80 else "❌" if avg_percentage <= 40 else "⚠️"
            result_text += f"• {microtopic_name} — {avg_percentage}% {status}\n"

        return result_text

    except Exception as e:
        print(f"Ошибка при получении детальной статистики предмета: {e}")
        return "❌ Ошибка при получении статистики"


async def get_subject_microtopics_summary(subject_id: int) -> str:
    """
    Получить сводку по сильным и слабым темам для предмета

    Args:
        subject_id: ID предмета

    Returns:
        str: Отформатированный текст со сводкой
    """
    try:
        from database.repositories import SubjectRepository, GroupRepository, StudentRepository, MicrotopicRepository

        # Получаем предмет
        subject = await SubjectRepository.get_by_id(subject_id)
        if not subject:
            return "❌ Предмет не найден"

        # Получаем группы предмета
        groups = await GroupRepository.get_by_subject(subject_id)
        if not groups:
            return f"📚 {subject.name}\n❌ Группы по данному предмету не найдены"

        # Получаем микротемы предмета
        microtopics = await MicrotopicRepository.get_by_subject(subject_id)
        if not microtopics:
            return f"📚 {subject.name}\n❌ Микротемы по данному предмету не найдены"

        # Собираем статистику по всем микротемам
        microtopic_stats = {}

        for group in groups:
            students = await StudentRepository.get_by_group(group.id)

            for student in students:
                student_microtopic_stats = await StudentRepository.get_microtopic_understanding(student.id, subject_id)

                for microtopic_number, stats in student_microtopic_stats.items():
                    if microtopic_number not in microtopic_stats:
                        microtopic_stats[microtopic_number] = []
                    microtopic_stats[microtopic_number].append(stats['percentage'])

        if not microtopic_stats:
            return f"📚 {subject.name}\n❌ Пока не выполнено ни одного задания для анализа сильных и слабых тем"

        # Создаем словарь названий микротем
        microtopic_names = {mt.number: mt.name for mt in microtopics}

        # Вычисляем средние значения и определяем сильные/слабые темы
        strong_topics = []
        weak_topics = []

        for microtopic_number, percentages in microtopic_stats.items():
            avg_percentage = round(sum(percentages) / len(percentages), 1) if percentages else 0
            microtopic_name = microtopic_names.get(microtopic_number, f"Микротема {microtopic_number}")

            if avg_percentage >= 80:
                strong_topics.append(microtopic_name)
            elif avg_percentage <= 40:
                weak_topics.append(microtopic_name)

        # Формируем результат
        result_text = f"📚 {subject.name}\n"

        if strong_topics:
            result_text += "🟢 Сильные темы (≥80%):\n"
            for topic in strong_topics:
                result_text += f"• {topic}\n"

        if weak_topics:
            if strong_topics:
                result_text += "\n"
            result_text += "🔴 Слабые темы (≤40%):\n"
            for topic in weak_topics:
                result_text += f"• {topic}\n"

        if not strong_topics and not weak_topics:
            result_text += "📊 Нет сильных или слабых тем"

        return result_text

    except Exception as e:
        print(f"Ошибка при получении сводки по предмету: {e}")
        return "❌ Ошибка при получении сводки"


async def get_general_microtopics_detailed() -> str:
    """
    Получить детальную статистику по микротемам для всех предметов

    Returns:
        str: Отформатированный текст с детальной статистикой
    """
    try:
        from database.repositories import SubjectRepository, GroupRepository, StudentRepository, MicrotopicRepository

        # Получаем все предметы
        all_subjects = await SubjectRepository.get_all()
        if not all_subjects:
            return "❌ Предметы не найдены"

        result_text = "📊 Общая статистика по микротемам\n📈 Средний % понимания по всем предметам:\n\n"

        has_data = False

        for subject in all_subjects:
            # Получаем группы предмета
            groups = await GroupRepository.get_by_subject(subject.id)
            if not groups:
                continue

            # Получаем микротемы предмета
            microtopics = await MicrotopicRepository.get_by_subject(subject.id)
            if not microtopics:
                continue

            # Собираем статистику по всем микротемам предмета
            microtopic_stats = {}

            for group in groups:
                students = await StudentRepository.get_by_group(group.id)

                for student in students:
                    student_microtopic_stats = await StudentRepository.get_microtopic_understanding(student.id, subject.id)

                    for microtopic_number, stats in student_microtopic_stats.items():
                        if microtopic_number not in microtopic_stats:
                            microtopic_stats[microtopic_number] = []
                        microtopic_stats[microtopic_number].append(stats['percentage'])

            if microtopic_stats:
                has_data = True
                result_text += f"📚 {subject.name}:\n"

                # Создаем словарь названий микротем
                microtopic_names = {mt.number: mt.name for mt in microtopics}

                # Сортируем по номеру микротемы
                for microtopic_number in sorted(microtopic_stats.keys()):
                    percentages = microtopic_stats[microtopic_number]
                    avg_percentage = round(sum(percentages) / len(percentages), 1) if percentages else 0
                    microtopic_name = microtopic_names.get(microtopic_number, f"Микротема {microtopic_number}")

                    # Определяем статус
                    status = "✅" if avg_percentage >= 80 else "❌" if avg_percentage <= 40 else "⚠️"
                    result_text += f"  • {microtopic_name} — {avg_percentage}% {status}\n"

                result_text += "\n"

        if not has_data:
            return "📊 Общая статистика по микротемам\n❌ Пока не выполнено ни одного задания по микротемам"

        return result_text.rstrip()

    except Exception as e:
        print(f"Ошибка при получении общей детальной статистики: {e}")
        return "❌ Ошибка при получении статистики"


async def get_general_microtopics_summary() -> str:
    """
    Получить сводку по сильным и слабым темам для всех предметов

    Returns:
        str: Отформатированный текст со сводкой
    """
    try:
        from database.repositories import SubjectRepository, GroupRepository, StudentRepository, MicrotopicRepository

        # Получаем все предметы
        all_subjects = await SubjectRepository.get_all()
        if not all_subjects:
            return "❌ Предметы не найдены"

        result_text = "📊 Общая сводка по микротемам\n"

        all_strong_topics = []
        all_weak_topics = []
        has_data = False

        for subject in all_subjects:
            # Получаем группы предмета
            groups = await GroupRepository.get_by_subject(subject.id)
            if not groups:
                continue

            # Получаем микротемы предмета
            microtopics = await MicrotopicRepository.get_by_subject(subject.id)
            if not microtopics:
                continue

            # Собираем статистику по всем микротемам предмета
            microtopic_stats = {}

            for group in groups:
                students = await StudentRepository.get_by_group(group.id)

                for student in students:
                    student_microtopic_stats = await StudentRepository.get_microtopic_understanding(student.id, subject.id)

                    for microtopic_number, stats in student_microtopic_stats.items():
                        if microtopic_number not in microtopic_stats:
                            microtopic_stats[microtopic_number] = []
                        microtopic_stats[microtopic_number].append(stats['percentage'])

            if microtopic_stats:
                has_data = True
                # Создаем словарь названий микротем
                microtopic_names = {mt.number: mt.name for mt in microtopics}

                # Вычисляем средние значения и определяем сильные/слабые темы
                for microtopic_number, percentages in microtopic_stats.items():
                    avg_percentage = round(sum(percentages) / len(percentages), 1) if percentages else 0
                    microtopic_name = microtopic_names.get(microtopic_number, f"Микротема {microtopic_number}")
                    topic_with_subject = f"{microtopic_name} ({subject.name})"

                    if avg_percentage >= 80:
                        all_strong_topics.append(topic_with_subject)
                    elif avg_percentage <= 40:
                        all_weak_topics.append(topic_with_subject)

        if not has_data:
            return "📊 Общая сводка по микротемам\n❌ Пока не выполнено ни одного задания для анализа"

        # Формируем результат
        if all_strong_topics:
            result_text += "\n🟢 Сильные темы (≥80%):\n"
            for topic in all_strong_topics:
                result_text += f"• {topic}\n"

        if all_weak_topics:
            if all_strong_topics:
                result_text += "\n"
            result_text += "🔴 Слабые темы (≤40%):\n"
            for topic in all_weak_topics:
                result_text += f"• {topic}\n"

        if not all_strong_topics and not all_weak_topics:
            result_text += "\n📊 Нет сильных или слабых тем"

        return result_text

    except Exception as e:
        print(f"Ошибка при получении общей сводки: {e}")
        return "❌ Ошибка при получении сводки"


def format_student_topics_stats(student_data: Dict) -> str:
    """
    УСТАРЕВШАЯ ФУНКЦИЯ: Форматировать статистику по темам ученика из статических данных

    Args:
        student_data: Данные ученика

    Returns:
        str: Отформатированный текст со статистикой
    """
    # Определяем сильные и слабые темы
    strong_topics = [topic for topic, percentage in student_data["topics"].items()
                    if percentage >= 80]
    weak_topics = [topic for topic, percentage in student_data["topics"].items()
                  if percentage <= 40]

    # Формируем текст с результатами
    result_text = f"📌 {student_data['name']}\n"
    result_text += "📈 % понимания по микротемам:\n"

    # Добавляем информацию о каждой теме
    for topic, percentage in student_data["topics"].items():
        status = "✅" if percentage >= 80 else "❌" if percentage <= 40 else "⚠️"
        result_text += f"• {topic} — {percentage}% {status}\n"

    # Добавляем информацию о сильных и слабых темах в более наглядном формате
    if strong_topics:
        result_text += "\n🟢 Сильные темы (≥80%):\n"
        for topic in strong_topics:
            result_text += f"• {topic}\n"

    if weak_topics:
        result_text += "\n🔴 Слабые темы (≤40%):\n"
        for topic in weak_topics:
            result_text += f"• {topic}\n"

    return result_text

def format_group_stats(group_data: Dict) -> str:
    """
    Форматировать статистику по группе в текстовый вид

    Args:
        group_data: Данные группы

    Returns:
        str: Отформатированный текст со статистикой
    """
    # Формируем текст с результатами
    result_text = f"👥 Группа: {group_data['name']}\n"
    result_text += f"📗 Предмет: {group_data['subject']}\n"
    result_text += f"📊 Средний % выполнения ДЗ: {group_data['homework_completion']}%\n\n"

    # Добавляем информацию о микротемах
    if group_data["topics"]:
        result_text += "📈 Средний % понимания по микротемам:\n"
        for topic, percentage in group_data["topics"].items():
            result_text += f"• {topic} — {percentage}%\n"
    else:
        result_text += "📈 Статистика по микротемам пока недоступна\n"
    
    # Добавляем рейтинг по баллам
    if group_data["rating"]:
        result_text += "\n📋 Рейтинг по баллам:\n"
        for i, student in enumerate(group_data["rating"], 1):
            result_text += f"{i}. {student['name']} — {student['points']} баллов\n"
    
    return result_text

def get_test_results(test_id: str, student_id: str) -> Dict:
    """
    Получить результаты теста для студента
    
    Args:
        test_id: Идентификатор теста
        student_id: Идентификатор студента
        
    Returns:
        Dict: Словарь с результатами теста
    """
    print(f"DEBUG: Запрос результатов теста: {test_id} для студента: {student_id}")
    
    # В реальном приложении здесь будет запрос к базе данных
    # Для примера используем фиксированные значения
    test_results = {
        "course_entry_chem": {
            "total_questions": 30,
            "correct_answers": 15,
            "topics_progress": {
                "Алканы": 90,
                "Изомерия": 33,
                "Кислоты": 60,
                "Циклоалканы": None  # None означает, что тема не проверена
            }
        },
        "course_entry_kz": {
            "total_questions": 30,
            "correct_answers": 20,
            "topics_progress": {
                "Древняя история": 80,
                "Средневековье": 60,
                "Новое время": 40,
                "Новейшая история": None  # None означает, что тема не проверена
            }
        },
        "month_entry_chem_1": {
            "total_questions": 30,
            "correct_answers": 15,
            "topics_progress": {
                "Алканы": 60,
                "Изомерия": 33,
                "Кислоты": 60
            }
        },
        "month_control_chem_1": {
            "total_questions": 30,
            "correct_answers": 20,
            "topics_progress": {
                "Алканы": 90,
                "Изомерия": 45,
                "Кислоты": 100
            }
        }
    }
    
    result = test_results.get(test_id, {
        "total_questions": 0,
        "correct_answers": 0,
        "topics_progress": {}
    })
    
    print(f"DEBUG: Возвращаемые результаты: {result}")
    return result

def format_test_result(test_results: Dict, subject_name: str, test_type: str, month: str = None) -> str:
    """
    Форматировать результаты теста в текстовый вид
    
    Args:
        test_results: Результаты теста
        subject_name: Название предмета
        test_type: Тип теста (course_entry, month_entry, month_control)
        month: Месяц (опционально)
        
    Returns:
        str: Отформатированный текст с результатами
    """
    # Определяем заголовок в зависимости от типа теста
    if test_type == "course_entry":
        result_text = f"📊 Входной тест курса пройден\nРезультат:\n📗 {subject_name}:\n"
    elif test_type == "month_entry":
        result_text = f"📊 Входной тест месяца {month} курса пройден\nРезультат:\n📗 {subject_name}:\n"
    elif test_type == "month_control":
        result_text = f"📊 Контрольный тест месяца {month} курса пройден\nРезультат:\n📗 {subject_name}:\n"
    else:
        result_text = f"📊 Тест пройден\nРезультат:\n📗 {subject_name}:\n"
    
    # Добавляем информацию о количестве правильных ответов
    result_text += f"Верных: {test_results['correct_answers']} / {test_results['total_questions']}\n"
    
    # Добавляем информацию о каждой теме с единым форматированием
    for topic, percentage in test_results["topics_progress"].items():
        if percentage is None:
            result_text += f"• {topic} — ❌ Не проверено\n"
        else:
            status = "✅" if percentage >= 80 else "❌" if percentage <= 40 else "⚠️"
            result_text += f"• {topic} — {percentage}% {status}\n"

    # Добавляем сильные и слабые темы
    result_text = add_strong_and_weak_topics(result_text, test_results["topics_progress"])
    
    return result_text


async def format_course_entry_test_result(test_result) -> str:
    """
    Форматировать результаты входного теста курса из БД (краткая версия для студента)

    Args:
        test_result: Объект CourseEntryTestResult из БД

    Returns:
        str: Отформатированный текст результатов
    """
    try:
        # Безопасно получаем название предмета
        subject_name = "Неизвестный предмет"
        if hasattr(test_result, 'subject') and test_result.subject:
            subject_name = test_result.subject.name

        # Основная информация без детальной статистики
        result_text = f"📊 Входной тест курса пройден\nРезультат:\n📗 {subject_name}:\n"
        result_text += f"Верных: {test_result.correct_answers} / {test_result.total_questions}\n"
        result_text += f"Процент: {int((test_result.correct_answers / test_result.total_questions) * 100)}%\n\n"
        result_text += "Выберите тип аналитики:"

        return result_text

    except Exception as e:
        print(f"Ошибка в format_course_entry_test_result: {e}")
        return f"📊 Входной тест курса пройден\nВерных: {test_result.correct_answers} / {test_result.total_questions}\n\n❌ Ошибка при загрузке статистики"


async def format_course_entry_test_detailed_microtopics(test_result) -> str:
    """
    Форматировать детальную статистику по микротемам входного теста курса

    Args:
        test_result: Объект CourseEntryTestResult из БД

    Returns:
        str: Отформатированный текст с процентами по микротемам
    """
    try:
        from database import MicrotopicRepository

        # Безопасно получаем название предмета
        subject_name = "Неизвестный предмет"
        if hasattr(test_result, 'subject') and test_result.subject:
            subject_name = test_result.subject.name

        # Основная информация
        result_text = f"📊 Входной тест курса - детальная статистика\n📗 {subject_name}:\n"
        result_text += f"Верных: {test_result.correct_answers} / {test_result.total_questions}\n\n"

        # Группируем результаты по микротемам
        microtopic_stats = {}

        if hasattr(test_result, 'question_results') and test_result.question_results:
            for question_result in test_result.question_results:
                microtopic_num = question_result.microtopic_number
                if microtopic_num is None:
                    continue

                if microtopic_num not in microtopic_stats:
                    microtopic_stats[microtopic_num] = {
                        'correct': 0,
                        'total': 0
                    }

                microtopic_stats[microtopic_num]['total'] += 1
                if question_result.is_correct:
                    microtopic_stats[microtopic_num]['correct'] += 1

        # Получаем названия микротем
        microtopic_names = {}
        try:
            microtopics = await MicrotopicRepository.get_by_subject(test_result.subject_id)
            microtopic_names = {mt.number: mt.name for mt in microtopics}
        except Exception as e:
            print(f"Ошибка при получении микротем: {e}")

        # Добавляем статистику по микротемам
        result_text += "📈 Проценты по микротемам:\n"
        for microtopic_num, stats in microtopic_stats.items():
            if stats['total'] > 0:
                percentage = int((stats['correct'] / stats['total']) * 100)
                microtopic_name = microtopic_names.get(microtopic_num, f"Микротема {microtopic_num}")

                status = "✅" if percentage >= 80 else "❌" if percentage <= 40 else "⚠️"
                result_text += f"• {microtopic_name} — {percentage}% {status}\n"

        return result_text

    except Exception as e:
        print(f"Ошибка в format_course_entry_test_detailed_microtopics: {e}")
        return f"📊 Входной тест курса\nВерных: {test_result.correct_answers} / {test_result.total_questions}\n\n❌ Ошибка при загрузке детальной статистики"


async def format_course_entry_test_summary_microtopics(test_result) -> str:
    """
    Форматировать сводку по сильным/слабым темам входного теста курса

    Args:
        test_result: Объект CourseEntryTestResult из БД

    Returns:
        str: Отформатированный текст с сильными и слабыми темами
    """
    try:
        from database import MicrotopicRepository

        # Безопасно получаем название предмета
        subject_name = "Неизвестный предмет"
        if hasattr(test_result, 'subject') and test_result.subject:
            subject_name = test_result.subject.name

        # Основная информация
        result_text = f"📊 Входной тест курса - сильные/слабые темы\n📗 {subject_name}:\n"
        result_text += f"Верных: {test_result.correct_answers} / {test_result.total_questions}\n\n"

        # Группируем результаты по микротемам
        microtopic_stats = {}

        if hasattr(test_result, 'question_results') and test_result.question_results:
            for question_result in test_result.question_results:
                microtopic_num = question_result.microtopic_number
                if microtopic_num is None:
                    continue

                if microtopic_num not in microtopic_stats:
                    microtopic_stats[microtopic_num] = {
                        'correct': 0,
                        'total': 0
                    }

                microtopic_stats[microtopic_num]['total'] += 1
                if question_result.is_correct:
                    microtopic_stats[microtopic_num]['correct'] += 1

        # Получаем названия микротем
        microtopic_names = {}
        try:
            microtopics = await MicrotopicRepository.get_by_subject(test_result.subject_id)
            microtopic_names = {mt.number: mt.name for mt in microtopics}
        except Exception as e:
            print(f"Ошибка при получении микротем: {e}")

        # Определяем сильные и слабые темы
        strong_topics = []
        weak_topics = []

        for microtopic_num, stats in microtopic_stats.items():
            if stats['total'] > 0:
                percentage = int((stats['correct'] / stats['total']) * 100)
                microtopic_name = microtopic_names.get(microtopic_num, f"Микротема {microtopic_num}")

                if percentage >= 80:
                    strong_topics.append(microtopic_name)
                elif percentage <= 40:
                    weak_topics.append(microtopic_name)

        # Добавляем сильные темы
        if strong_topics:
            result_text += "🟢 Сильные темы (≥80%):\n"
            for topic in strong_topics:
                result_text += f"• {topic}\n"
        else:
            result_text += "🟢 Сильные темы (≥80%): нет\n"

        result_text += "\n"

        # Добавляем слабые темы
        if weak_topics:
            result_text += "🔴 Слабые темы (≤40%):\n"
            for topic in weak_topics:
                result_text += f"• {topic}\n"
        else:
            result_text += "🔴 Слабые темы (≤40%): нет\n"

        return result_text

    except Exception as e:
        print(f"Ошибка в format_course_entry_test_summary_microtopics: {e}")
        return f"📊 Входной тест курса\nВерных: {test_result.correct_answers} / {test_result.total_questions}\n\n❌ Ошибка при загрузке сводки"

def format_test_comparison(entry_results: Dict, control_results: Dict, subject_name: str, month: str) -> str:
    """
    Форматировать сравнение входного и контрольного тестов
    
    Args:
        entry_results: Результаты входного теста
        control_results: Результаты контрольного теста
        subject_name: Название предмета
        month: Месяц
        
    Returns:
        str: Отформатированный текст со сравнением
    """
    # Вычисляем общий прирост
    entry_topics = entry_results["topics_progress"]
    control_topics = control_results["topics_progress"]
    
    if entry_topics and control_topics:
        entry_avg = sum(entry_topics.values()) / len(entry_topics)
        control_avg = sum(control_topics.values()) / len(control_topics)
        growth = int(control_avg - entry_avg)
    else:
        growth = 0
    
    # Формируем текст с результатами
    result_text = f"🧾 Сравнение входного и контрольного теста месяца по предмету:\n📗 {subject_name}:\n"
    result_text += f"Верных: {entry_results['correct_answers']} / {entry_results['total_questions']} → {control_results['correct_answers']} / {control_results['total_questions']}\n"
    
    # Добавляем информацию о каждой теме
    for topic in entry_topics:
        if topic in control_topics:
            entry_percentage = entry_topics[topic]
            control_percentage = control_topics[topic]
            result_text += f"• {topic} — {entry_percentage}% → {control_percentage}%\n"
    
    result_text += f"\n📈 Общий прирост: +{growth}%\n"
    
    result_text = add_strong_and_weak_topics(result_text, control_topics)
    
    return result_text

def add_strong_and_weak_topics(result_text: str, topics: dict) -> str:
    """
    Добавить информацию о сильных и слабых темах к тексту результата

    Args:
        result_text: Исходный текст
        topics: Словарь с темами и процентами

    Returns:
        str: Текст с добавленной информацией о сильных и слабых темах
    """
    # Определяем сильные и слабые темы по результатам теста
    strong_topics = [topic for topic, percentage in topics.items()
                    if percentage is not None and percentage >= 80]
    weak_topics = [topic for topic, percentage in topics.items()
                  if percentage is not None and percentage <= 40]

    # Добавляем информацию о сильных и слабых темах
    if strong_topics:
        result_text += "\n🟢 Сильные темы (≥80%):\n"
        for topic in strong_topics:
            result_text += f"• {topic}\n"

    if weak_topics:
        result_text += "\n🔴 Слабые темы (≤40%):\n"
        for topic in weak_topics:
            result_text += f"• {topic}\n"

    return result_text
    

async def get_subject_stats(subject_id: str) -> dict:
    """
    Получить статистику по предмету из реальной базы данных

    Args:
        subject_id: ID предмета

    Returns:
        dict: Данные о статистике предмета
    """
    try:
        from database.repositories import SubjectRepository, GroupRepository, StudentRepository, MicrotopicRepository

        # Получаем предмет
        subject = await SubjectRepository.get_by_id(int(subject_id))
        if not subject:
            return {
                "subject_id": subject_id,
                "name": "Неизвестный предмет",
                "groups": []
            }

        # Получаем группы предмета
        groups = await GroupRepository.get_by_subject(int(subject_id))

        groups_data = []

        for group in groups:
            # Получаем статистику группы (используем существующую функцию)
            group_stats = await get_group_stats(str(group.id))

            groups_data.append({
                "group_id": str(group.id),
                "name": group_stats["name"],
                "homework_completion": group_stats["homework_completion"],
                "test_reports_growth": group_stats["test_reports_growth"],
                "topics": group_stats["topics"],
                "rating": group_stats["rating"],
                "students_count": len(group_stats["rating"])  # Добавляем количество студентов
            })

        # Получаем средневзвешенный рост по тест-отчетам для предмета
        avg_test_reports_growth = await get_subject_test_reports_growth(int(subject_id))

        return {
            "subject_id": subject_id,
            "name": subject.name,
            "test_reports_growth": round(avg_test_reports_growth, 1) if avg_test_reports_growth is not None else None,
            "groups": groups_data
        }

    except Exception as e:
        print(f"Ошибка при получении статистики предмета: {e}")
        return {
            "subject_id": subject_id,
            "name": "Ошибка загрузки",
            "groups": []
        }

async def get_course_stats(course_id: str) -> dict:
    """
    Получить статистику по курсу из реальной базы данных

    Args:
        course_id: ID курса

    Returns:
        dict: Данные о статистике курса
    """
    try:
        from database.repositories import CourseRepository, SubjectRepository, GroupRepository, StudentRepository

        # Получаем курс
        course = await CourseRepository.get_by_id(int(course_id))
        if not course:
            return {
                "course_id": course_id,
                "name": "Неизвестный курс",
                "subjects": []
            }

        # Получаем предметы курса
        subjects = course.subjects

        subjects_data = []

        for subject in subjects:
            # Получаем статистику предмета (используем существующую функцию)
            subject_stats = await get_subject_stats(str(subject.id))

            # Вычисляем взвешенный средний процент выполнения ДЗ по всем группам предмета
            if subject_stats["groups"]:
                total_weighted_homework = 0
                total_students = 0

                for group in subject_stats["groups"]:
                    students_count = group.get('students_count', 0)
                    if students_count > 0:
                        total_weighted_homework += group['homework_completion'] * students_count
                        total_students += students_count

                avg_homework = round(total_weighted_homework / total_students, 1) if total_students > 0 else 0
            else:
                avg_homework = 0

            subjects_data.append({
                "subject_id": str(subject.id),
                "name": subject_stats["name"],
                "homework_completion": avg_homework,
                "test_reports_growth": subject_stats["test_reports_growth"],
                "groups_count": len(subject_stats["groups"]),
                "students_count": sum(group.get('students_count', 0) for group in subject_stats["groups"]),
                "groups": subject_stats["groups"]
            })

        # Получаем средневзвешенный рост по тест-отчетам для курса
        avg_course_test_reports_growth = await get_course_test_reports_growth(int(course_id))

        return {
            "course_id": course_id,
            "name": course.name,
            "test_reports_growth": round(avg_course_test_reports_growth, 1) if avg_course_test_reports_growth is not None else None,
            "subjects": subjects_data
        }

    except Exception as e:
        print(f"Ошибка при получении статистики курса: {e}")
        return {
            "course_id": course_id,
            "name": "Ошибка загрузки",
            "subjects": []
        }








async def get_group_test_reports_growth_weighted(group_id: int) -> float:
    """
    Получить средневзвешенный процент роста по тест-отчетам для группы
    Вес каждого студента зависит от количества пройденных им пар тестов

    Args:
        group_id: ID группы

    Returns:
        float: Средневзвешенный процент роста или None если нет данных
    """
    try:
        from database.repositories import StudentRepository, MonthEntryTestResultRepository, MonthControlTestResultRepository, GroupRepository, MonthTestRepository

        # Получаем группу для определения предмета
        group = await GroupRepository.get_by_id(group_id)
        if not group or not group.subject_id:
            return None

        # Получаем всех студентов группы
        students = await StudentRepository.get_by_group(group_id)

        if not students:
            return None

        # Получаем все тесты месяца для предмета группы
        all_month_tests = await MonthTestRepository.get_all()
        subject_month_tests = [mt for mt in all_month_tests if mt.subject_id == group.subject_id]
        subject_test_ids = {mt.id for mt in subject_month_tests}

        total_weighted_growth = 0
        total_weight = 0

        for student in students:
            # Получаем все входные тесты студента
            entry_results = await MonthEntryTestResultRepository.get_by_student(student.id)

            student_growth_sum = 0
            student_pairs_count = 0

            for entry_result in entry_results:
                # Учитываем только тесты по предмету данной группы
                if entry_result.month_test_id not in subject_test_ids:
                    continue

                # Ищем соответствующий контрольный тест
                control_result = await MonthControlTestResultRepository.get_by_student_and_month_test(
                    student.id, entry_result.month_test_id
                )

                if control_result:
                    # Есть пара - рассчитываем рост
                    entry_percentage = entry_result.score_percentage
                    control_percentage = control_result.score_percentage

                    if entry_percentage > 0:
                        # Относительный рост: ((B - A) / A) × 100%
                        growth = ((control_percentage - entry_percentage) / entry_percentage) * 100
                    else:
                        # Абсолютный рост в процентных пунктах
                        growth = control_percentage

                    student_growth_sum += growth
                    student_pairs_count += 1

            # Если у студента есть пары тестов, добавляем его в расчет
            if student_pairs_count > 0:
                student_avg_growth = student_growth_sum / student_pairs_count
                total_weighted_growth += student_avg_growth * student_pairs_count
                total_weight += student_pairs_count

        # Возвращаем средневзвешенный рост
        if total_weight > 0:
            return total_weighted_growth / total_weight
        else:
            return None

    except Exception as e:
        print(f"Ошибка при получении средневзвешенного роста тест-отчетов группы {group_id}: {e}")
        return None


async def get_subject_test_reports_growth(subject_id: int) -> float:
    """
    Получить средний процент роста по тест-отчетам для предмета

    Args:
        subject_id: ID предмета

    Returns:
        float: Средний процент роста или None если нет данных
    """
    try:
        from database.repositories import GroupRepository

        # Получаем все группы предмета
        groups = await GroupRepository.get_by_subject(subject_id)

        if not groups:
            return None

        total_weighted_growth = 0
        total_weight = 0

        for group in groups:
            # Получаем рост группы и количество студентов как вес
            group_growth = await get_group_test_reports_growth_weighted(group.id)
            if group_growth is not None:
                # Получаем количество студентов группы как вес
                from database.repositories import StudentRepository
                students = await StudentRepository.get_by_group(group.id)
                group_weight = len(students) if students else 0

                if group_weight > 0:
                    total_weighted_growth += group_growth * group_weight
                    total_weight += group_weight

        # Возвращаем средневзвешенный рост по всем группам предмета
        if total_weight > 0:
            return total_weighted_growth / total_weight
        else:
            return None

    except Exception as e:
        print(f"Ошибка при получении роста тест-отчетов предмета {subject_id}: {e}")
        return None


async def get_course_test_reports_growth(course_id: int) -> float:
    """
    Получить средний процент роста по тест-отчетам для курса

    Args:
        course_id: ID курса

    Returns:
        float: Средний процент роста или None если нет данных
    """
    try:
        from database.repositories import CourseRepository

        # Получаем курс с предметами
        course = await CourseRepository.get_by_id(course_id)

        if not course or not course.subjects:
            return None

        total_weighted_growth = 0
        total_weight = 0

        for subject in course.subjects:
            subject_growth = await get_subject_test_reports_growth(subject.id)
            if subject_growth is not None:
                # Получаем количество студентов в группах предмета как вес
                from database.repositories import GroupRepository, StudentRepository
                groups = await GroupRepository.get_by_subject(subject.id)

                subject_students_count = 0
                for group in groups:
                    students = await StudentRepository.get_by_group(group.id)
                    subject_students_count += len(students) if students else 0

                if subject_students_count > 0:
                    total_weighted_growth += subject_growth * subject_students_count
                    total_weight += subject_students_count

        # Возвращаем средневзвешенный рост по всем студентам курса
        if total_weight > 0:
            return total_weighted_growth / total_weight
        else:
            return None

    except Exception as e:
        print(f"Ошибка при получении роста тест-отчетов курса {course_id}: {e}")
        return None


async def get_general_stats() -> dict:
    """
    Получить общую статистику по всем курсам из реальной базы данных

    Returns:
        dict: Общие данные статистики с агрегацией по курсам
    """
    try:
        from database.repositories import StudentRepository, GroupRepository, SubjectRepository, CourseRepository

        # Получаем общее количество студентов
        all_students = await StudentRepository.get_all()
        total_students = len(all_students)

        # Считаем активных студентов (у которых есть группа)
        active_students = len([s for s in all_students if s.groups])

        # Получаем общее количество групп
        all_groups = await GroupRepository.get_all()
        total_groups = len(all_groups)

        # Получаем статистику по курсам
        all_courses = await CourseRepository.get_all()
        courses_stats = []
        total_weighted_growth = 0
        total_weight = 0

        for course in all_courses:
            # Получаем статистику курса
            course_data = await get_course_stats(str(course.id))

            # Вычисляем взвешенный средний процент по предметам курса
            if course_data["subjects"]:
                total_weighted_completion = 0
                total_students = 0

                for subject in course_data["subjects"]:
                    students_count = subject.get('students_count', 0)
                    if students_count > 0:
                        total_weighted_completion += subject['homework_completion'] * students_count
                        total_students += students_count

                avg_course_completion = round(total_weighted_completion / total_students, 1) if total_students > 0 else 0
            else:
                avg_course_completion = 0

            course_students_count = sum(subject.get('students_count', 0) for subject in course_data["subjects"])

            courses_stats.append({
                "name": course.name,
                "completion_rate": avg_course_completion,
                "test_reports_growth": course_data["test_reports_growth"],
                "subjects_count": len(course_data["subjects"]),
                "students_count": course_students_count
            })

            # Собираем значения роста для общей статистики (средневзвешенное)
            if course_data["test_reports_growth"] is not None and course_students_count > 0:
                total_weighted_growth += course_data["test_reports_growth"] * course_students_count
                total_weight += course_students_count

        # Вычисляем общий процент выполнения ДЗ напрямую по всем студентам
        # Это даст точный результат, как в SQL запросе
        total_unique_completed = 0
        total_available = 0

        for student in all_students:
            if student.groups:  # Только активные студенты
                # Получаем статистику студента
                student_stats = await StudentRepository.get_general_stats(student.id)
                total_unique_completed += student_stats.get('unique_completed', 0)
                total_available += student_stats.get('total_available', 0)

        overall_completion = round((total_unique_completed / total_available * 100), 1) if total_available > 0 else 0

        # Вычисляем общий средневзвешенный рост по тест-отчетам
        if total_weight > 0:
            overall_test_reports_growth = total_weighted_growth / total_weight
        else:
            overall_test_reports_growth = None

        return {
            "total_students": total_students,
            "active_students": active_students,
            "total_groups": total_groups,
            "total_courses": len(all_courses),
            "courses": courses_stats,
            "overall_completion": round(overall_completion, 1),
            "overall_test_reports_growth": round(overall_test_reports_growth, 1) if overall_test_reports_growth is not None else None,
            "monthly_progress": {
                "Данные": "В разработке",
                "по месяцам": "будут добавлены",
                "позже": "..."
            }
        }

    except Exception as e:
        print(f"Ошибка при получении общей статистики: {e}")
        return {
            "total_students": 0,
            "active_students": 0,
            "total_groups": 0,
            "total_courses": 0,
            "courses": [],
            "overall_completion": 0,
            "overall_test_reports_growth": None,
            "monthly_progress": {}
        }

def format_subject_stats(subject_data: dict) -> str:
    """
    Форматировать статистику по предмету в текстовый вид
    
    Args:
        subject_data: Данные о предмете
        
    Returns:
        str: Отформатированный текст со статистикой
    """
    result_text = f"📊 Статистика по предмету: {subject_data['name']}\n\n"
    
    # Добавляем информацию о группах
    result_text += "👨‍👩‍👧‍👦 Группы:\n"
    for group in subject_data["groups"]:
        result_text += f"• {group['name']} - выполнение ДЗ: {group['homework_completion']}%\n"
    
    # Добавляем информацию о средних показателях по темам
    result_text += "\n📈 Средние показатели по темам:\n"
    
    # Собираем все темы из всех групп
    all_topics = {}
    for group in subject_data["groups"]:
        for topic, percentage in group["topics"].items():
            if topic in all_topics:
                all_topics[topic].append(percentage)
            else:
                all_topics[topic] = [percentage]
    
    # Вычисляем средние значения и выводим
    for topic, percentages in all_topics.items():
        avg_percentage = sum(percentages) / len(percentages)
        result_text += f"• {topic} — {avg_percentage:.1f}%\n"
    
    return result_text

def format_course_stats(course_data: dict) -> str:
    """
    Форматировать статистику по курсу в текстовый вид

    Args:
        course_data: Данные о курсе

    Returns:
        str: Отформатированный текст со статистикой
    """
    result_text = f"📚 Курс: {course_data['name']}\n\n"
    result_text += f"📖 Количество предметов: {len(course_data['subjects'])}\n"

    if course_data['subjects']:
        # Вычисляем средневзвешенный процент выполнения ДЗ по курсу
        total_weighted_homework = 0
        total_students = 0

        for subject in course_data['subjects']:
            students_count = subject.get('students_count', 0)
            if students_count > 0:
                total_weighted_homework += subject['homework_completion'] * students_count
                total_students += students_count

        avg_homework = round(total_weighted_homework / total_students, 1) if total_students > 0 else 0
        result_text += f"📊 Средний % выполнения ДЗ: {avg_homework}%\n"

        # Добавляем средний рост по тест-отчетам
        if course_data.get('test_reports_growth') is not None:
            growth_value = course_data['test_reports_growth']
            if growth_value >= 0:
                result_text += f"📈 Средний % роста по тест-отчетам: +{growth_value}%\n\n"
            else:
                result_text += f"📉 Средний % роста по тест-отчетам: {growth_value}%\n\n"
        else:
            result_text += f"📈 Средний % роста по тест-отчетам: Н/Д\n\n"

        # Показываем список предметов
        result_text += "📋 Предметы:\n"
        for subject in course_data['subjects']:
            subject_text = f"• {subject['name']} — ДЗ: {subject['homework_completion']}%"
            if subject.get('test_reports_growth') is not None:
                growth = subject['test_reports_growth']
                if growth >= 0:
                    subject_text += f" | Рост: +{growth}%"
                else:
                    subject_text += f" | Рост: {growth}%"
            else:
                subject_text += f" | Рост: Н/Д"
            subject_text += f" (групп: {subject['groups_count']})\n"
            result_text += subject_text
    else:
        result_text += "❌ Предметы не найдены\n"

    return result_text


async def get_course_microtopics_detailed(course_id: int) -> str:
    """
    Получить детальную статистику по микротемам курса

    Args:
        course_id: ID курса

    Returns:
        str: Отформатированный текст со статистикой
    """
    try:
        from database.repositories import CourseRepository, SubjectRepository, MicrotopicRepository, StudentRepository, GroupRepository

        # Получаем курс
        course = await CourseRepository.get_by_id(course_id)
        if not course:
            return "❌ Курс не найден"

        # Получаем предметы курса
        subjects = course.subjects
        if not subjects:
            return f"🎓 {course.name}\n❌ В курсе нет предметов"

        result_text = f"🎓 {course.name}\n📈 Средний % понимания по микротемам:\n\n"

        for subject in subjects:
            # Получаем микротемы предмета
            microtopics = await MicrotopicRepository.get_by_subject(subject.id)
            if not microtopics:
                continue

            result_text += f"📚 {subject.name}:\n"

            # Получаем группы предмета
            groups = await GroupRepository.get_by_subject(subject.id)

            # Собираем статистику по всем микротемам предмета
            microtopic_stats = {}
            total_students = 0

            for group in groups:
                students = await StudentRepository.get_by_group(group.id)
                total_students += len(students)

                for student in students:
                    student_microtopic_stats = await StudentRepository.get_microtopic_understanding(student.id, subject.id)

                    for microtopic_number, stats in student_microtopic_stats.items():
                        if microtopic_number not in microtopic_stats:
                            microtopic_stats[microtopic_number] = []
                        microtopic_stats[microtopic_number].append(stats['percentage'])

            if microtopic_stats:
                # Создаем словарь названий микротем
                microtopic_names = {mt.number: mt.name for mt in microtopics}

                # Сортируем по номеру микротемы
                for microtopic_number in sorted(microtopic_stats.keys()):
                    percentages = microtopic_stats[microtopic_number]
                    avg_percentage = round(sum(percentages) / len(percentages), 1) if percentages else 0
                    microtopic_name = microtopic_names.get(microtopic_number, f"Микротема {microtopic_number}")

                    # Определяем статус
                    status = "✅" if avg_percentage >= 80 else "❌" if avg_percentage <= 40 else "⚠️"
                    result_text += f"  • {microtopic_name} — {avg_percentage}% {status}\n"
            else:
                result_text += "  ❌ Нет данных по микротемам\n"

            result_text += "\n"

        return result_text

    except Exception as e:
        print(f"Ошибка при получении статистики курса по микротемам: {e}")
        return "❌ Ошибка при загрузке статистики"


async def get_course_microtopics_summary(course_id: int) -> str:
    """
    Получить сводку по сильным и слабым темам курса

    Args:
        course_id: ID курса

    Returns:
        str: Отформатированный текст со сводкой
    """
    try:
        from database.repositories import CourseRepository, SubjectRepository, MicrotopicRepository, StudentRepository, GroupRepository

        # Получаем курс
        course = await CourseRepository.get_by_id(course_id)
        if not course:
            return "❌ Курс не найден"

        # Получаем предметы курса
        subjects = course.subjects
        if not subjects:
            return f"🎓 {course.name}\n❌ В курсе нет предметов"

        strong_topics = []
        weak_topics = []

        for subject in subjects:
            # Получаем микротемы предмета
            microtopics = await MicrotopicRepository.get_by_subject(subject.id)
            if not microtopics:
                continue

            # Получаем группы предмета
            groups = await GroupRepository.get_by_subject(subject.id)

            # Собираем статистику по всем микротемам предмета
            microtopic_stats = {}

            for group in groups:
                students = await StudentRepository.get_by_group(group.id)

                for student in students:
                    student_microtopic_stats = await StudentRepository.get_microtopic_understanding(student.id, subject.id)

                    for microtopic_number, stats in student_microtopic_stats.items():
                        if microtopic_number not in microtopic_stats:
                            microtopic_stats[microtopic_number] = []
                        microtopic_stats[microtopic_number].append(stats['percentage'])

            if microtopic_stats:
                # Создаем словарь названий микротем
                microtopic_names = {mt.number: mt.name for mt in microtopics}

                # Анализируем каждую микротему
                for microtopic_number, percentages in microtopic_stats.items():
                    avg_percentage = round(sum(percentages) / len(percentages), 1) if percentages else 0
                    microtopic_name = microtopic_names.get(microtopic_number, f"Микротема {microtopic_number}")
                    full_name = f"{subject.name}: {microtopic_name}"

                    if avg_percentage >= 80:
                        strong_topics.append((full_name, avg_percentage))
                    elif avg_percentage <= 40:
                        weak_topics.append((full_name, avg_percentage))

        # Формируем результат
        result_text = f"💪 Сильные и слабые темы курса\n\n🎓 {course.name}\n\n"

        if strong_topics:
            result_text += "✅ Сильные темы (≥80%):\n"
            for name, percentage in sorted(strong_topics, key=lambda x: x[1], reverse=True):
                result_text += f"• {name} — {percentage}%\n"
            result_text += "\n"
        else:
            result_text += "✅ Сильных тем пока нет\n\n"

        if weak_topics:
            result_text += "❌ Слабые темы (≤40%):\n"
            for name, percentage in sorted(weak_topics, key=lambda x: x[1]):
                result_text += f"• {name} — {percentage}%\n"
        else:
            result_text += "❌ Слабых тем нет\n"

        return result_text

    except Exception as e:
        print(f"Ошибка при получении сводки курса: {e}")
        return "❌ Ошибка при загрузке сводки"


async def show_student_analytics(callback: CallbackQuery, state: FSMContext, role: str):
    """
    Базовый обработчик для показа статистики по ученику

    Args:
        callback: Объект CallbackQuery
        state: Контекст состояния FSM
        role: Роль пользователя (curator)
    """
    student_id = await check_if_id_in_callback_data("analytics_student_", callback, state, "student")

    # Получаем студента для определения предмета
    student = await StudentRepository.get_by_id(int(student_id))
    if not student or not student.groups:
        await callback.message.edit_text(
            "❌ Студент не найден или не назначен в группу",
            reply_markup=get_back_to_analytics_kb()
        )
        return

    # Получаем данные состояния для определения выбранной группы
    state_data = await state.get_data()
    selected_group_id = state_data.get('selected_group') or state_data.get('group')

    # Определяем группу для отображения статистики
    target_group = None
    if selected_group_id:
        # Ищем выбранную группу среди групп студента
        for group in student.groups:
            if str(group.id) == str(selected_group_id):
                target_group = group
                break

    # Если выбранная группа не найдена, берем первую группу
    if not target_group:
        target_group = student.groups[0]

    if not target_group.subject:
        await callback.message.edit_text(
            "❌ У группы студента не указан предмет",
            reply_markup=get_back_to_analytics_kb()
        )
        return

    # Получаем реальные данные о студенте из базы данных (без детальной статистики по микротемам)
    general_stats = await StudentRepository.get_general_stats(int(student_id))

    # Формируем базовую информацию
    group_names = [group.name for group in student.groups]
    result_text = f"👤 Студент: {student.user.name}\n"
    result_text += f"📚 Группы: {', '.join(group_names)}\n"
    result_text += f"💎 Тариф: {student.tariff or 'Не указан'}\n\n"
    result_text += f"📊 Общая статистика:\n"
    result_text += f"   • Баллы: {general_stats.get('total_points', 0)}\n"
    result_text += f"   • Уровень: {student.level}\n"
    result_text += f"   • Выполнено ДЗ: {general_stats.get('unique_completed', 0)} (всего попыток: {general_stats.get('total_completed', 0)})\n\n"
    result_text += f"📗 Предмет: {target_group.subject.name}\n"
    result_text += "Выберите, что хотите посмотреть:"

    # Импортируем клавиатуру
    from common.analytics.keyboards import get_student_microtopics_kb

    await callback.message.edit_text(
        result_text,
        reply_markup=get_student_microtopics_kb(int(student_id), target_group.subject.id)
    )


async def show_group_analytics(callback: CallbackQuery, state: FSMContext, role: str):
    """
    Базовый обработчик для показа статистики по группе

    Args:
        callback: Объект CallbackQuery
        state: Контекст состояния FSM
        role: Роль пользователя (curator)
    """
    group_id = await check_if_id_in_callback_data("analytics_group_", callback, state, "group")

    # Получаем данные о группе из общего компонента
    group_data = await get_group_stats(group_id)

    # Формируем базовую информацию о группе
    result_text = f"👥 Группа: {group_data['name']}\n"
    result_text += f"📗 Предмет: {group_data['subject']}\n"
    result_text += f"📊 Средний % выполнения ДЗ: {group_data['homework_completion']}%\n"

    # Добавляем средний рост по тест-отчетам
    if group_data.get('test_reports_growth') is not None:
        growth_value = group_data['test_reports_growth']
        if growth_value >= 0:
            result_text += f"📈 Средний % роста по тест-отчетам: +{growth_value}%\n\n"
        else:
            result_text += f"📉 Средний % роста по тест-отчетам: {growth_value}%\n\n"
    else:
        result_text += f"📈 Средний % роста по тест-отчетам: Н/Д\n\n"

    result_text += "Выберите, что хотите посмотреть:"

    # Импортируем клавиатуру
    from common.analytics.keyboards import get_group_analytics_kb

    await callback.message.edit_text(
        result_text,
        reply_markup=get_group_analytics_kb(int(group_id))
    )


async def show_group_microtopics_detailed(callback: CallbackQuery, state: FSMContext):
    """
    Показать детальную статистику по микротемам группы в виде красивой таблицы

    Args:
        callback: Объект CallbackQuery
        state: Контекст состояния FSM
    """
    # Извлекаем group_id из callback_data
    # Формат: group_microtopics_detailed_GROUP_ID
    parts = callback.data.split("_")
    if len(parts) >= 4:
        group_id = int(parts[3])

        from common.microtopics.handlers import show_group_microtopics_universal
        from common.analytics.keyboards import get_back_to_analytics_kb

        # Определяем состояние в зависимости от текущего состояния пользователя
        current_state = await state.get_state()

        # Определяем роль и состояния на основе текущего состояния
        if "CuratorAnalyticsStates" in str(current_state):
            from curator.handlers.analytics import CuratorAnalyticsStates
            target_state = CuratorAnalyticsStates.group_microtopics
            role = "curator"
            callback_prefix = "curator_microtopics_page"  # Используем тот же префикс, что и для студентов
        elif "TeacherAnalyticsStates" in str(current_state):
            from teacher.handlers.analytics import TeacherAnalyticsStates
            target_state = TeacherAnalyticsStates.group_microtopics
            role = "teacher"
            callback_prefix = "teacher_microtopics_page"  # Используем тот же префикс, что и для студентов
        elif "ManagerAnalyticsStates" in str(current_state):
            from manager.handlers.analytics import ManagerAnalyticsStates
            target_state = ManagerAnalyticsStates.group_microtopics
            role = "manager"
            callback_prefix = "manager_microtopics_page"  # Используем тот же префикс, что и для студентов
        else:
            # Fallback для неизвестных состояний
            target_state = None
            role = "unknown"
            callback_prefix = "microtopics_page"

        await show_group_microtopics_universal(
            callback=callback,
            state=state,
            group_id=group_id,
            role=role,
            target_state=target_state,
            callback_prefix=callback_prefix,
            back_keyboard_func=lambda: get_back_to_analytics_kb(),
            title="👥 Статистика группы\n📈 Средний % понимания по микротемам",
            items_per_page=15,
            caption="📊 Статистика группы по микротемам",
            premium_check=False  # Все роли имеют доступ к статистике групп
        )
    else:
        from common.analytics.keyboards import get_back_to_analytics_kb
        await callback.message.edit_text(
            "❌ Ошибка в данных запроса",
            reply_markup=get_back_to_analytics_kb()
        )


async def show_group_rating(callback: CallbackQuery, state: FSMContext):
    """
    Показать рейтинг по баллам группы

    Args:
        callback: Объект CallbackQuery
        state: Контекст состояния FSM
    """
    # Извлекаем group_id из callback_data
    # Формат: group_rating_GROUP_ID
    parts = callback.data.split("_")
    if len(parts) >= 3:
        group_id = int(parts[2])

        # Получаем данные о группе
        group_data = await get_group_stats(str(group_id))

        # Формируем текст только с рейтингом
        result_text = f"👥 Группа: {group_data['name']}\n"
        result_text += f"📗 Предмет: {group_data['subject']}\n\n"

        # Добавляем рейтинг по баллам
        if group_data["rating"]:
            result_text += "📋 Рейтинг по баллам:\n"
            for i, student in enumerate(group_data["rating"], 1):
                result_text += f"{i}. {student['name']} — {student['points']} баллов\n"
        else:
            result_text += "📋 Рейтинг пока недоступен\n"

        # Импортируем клавиатуру
        from common.analytics.keyboards import get_back_to_analytics_kb

        await callback.message.edit_text(
            result_text,
            reply_markup=get_back_to_analytics_kb()
        )
    else:
        from common.analytics.keyboards import get_back_to_analytics_kb
        await callback.message.edit_text(
            "❌ Ошибка в данных запроса",
            reply_markup=get_back_to_analytics_kb()
        )





